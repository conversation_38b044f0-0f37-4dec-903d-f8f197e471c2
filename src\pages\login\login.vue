<template>
  <view class="login-page">
    <image class="logo" src="../../static/logo.png" />
    <text v-if="loginForm.loginType == 1" class="login-type-tip">账号密码登录</text>
    <text v-if="loginForm.loginType == 2" class="login-type-tip">手机验证码登录</text>
    <text class="login-tip">医师您好，欢迎使用滇医通医师端</text>
    <view v-if="loginForm.loginType == 2" class="login-form">
      <view class="login-form__item">
        <uni-easyinput v-model="loginForm.username" :clearable="false" placeholder="请输入手机号码" />
      </view>
      <view class="login-form__item">
        <uni-easyinput
          :value="loginForm.credentials"
          :clearable="false"
          placeholder="请输入手机验证码"
          :maxlength="6"
          @input="bindCodeChange"
        >
          <template #right>
            <button
              plain
              size="mini"
              style="border: 0px"
              :disabled="telCodeDuration > 0"
              @click="getCode"
              @change="bindCredentialsCode"
            >
              {{ telCodeDuration > 0 ? '重新获取' + telCodeDuration + 's' : '获取验证码' }}
            </button>
          </template>
        </uni-easyinput>
      </view>
    </view>
    <view v-if="loginForm.loginType == 1" class="login-form">
      <view class="login-form__item">
        <uni-easyinput v-model="loginForm.username" :clearable="false" placeholder="请输入账号名" />
      </view>
      <view class="login-form__item">
        <uni-easyinput
          v-model="loginForm.credentials"
          :clearable="false"
          type="password"
          placeholder="请输入密码"
        />
      </view>
    </view>
    <view class="tooltip">
      <text @click="toResetPassword">忘记密码</text>
      <text v-if="loginForm.loginType == 2" @click="switchLoginType(1)">使用密码登录</text>
      <text v-if="loginForm.loginType == 1" @click="switchLoginType(2)">使用验证码登录</text>
    </view>
    <button class="login-btn" :disabled="!allowLogin" @click="login">登录</button>
    <checkbox-group class="agree-label" @change="changeAgree">
      <label>
        <checkbox class="agree-checkbox" :checked="agreeCheck[0]" color="#1bb2b2" />
        我已阅读并同意
        <text class="service-tip" @click.stop="toAgreement">《服务协议》</text>
        和
        <text class="service-tip" @click.stop="toPrivacy">《隐私政策》</text>
      </label>
    </checkbox-group>
    <view class="content-bottom">
      <view class="bottom-toolbar">
        <view @click="toRegister">医师注册</view>
        <view @click="toService">联系客服</view>
      </view>
      <view class="bottom-tip">如果您是患者，请在微信内搜索公众号"滇医通官方号"使用挂号等服务</view>
      <view v-if="appVersion" class="bottom-tip">当前版本: {{ appVersion }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import debounce from 'lodash.debounce';
  import AuthApi from '@/apis/public/auth-api';
  import AppGlobal from '@/config/AppGlobal';
  import { useAppStore } from '@/store/appStore';
  import { useUserStore } from '@/store/userStore';
  import AppRouter from '@/utils/AppRouter';
  import AppToast from '@/utils/AppToast';
  import { onShow } from '@dcloudio/uni-app';
  import { computed, reactive, ref } from 'vue';
  import dayjs from 'dayjs';

  const { appVersion } = useAppStore();
  const userStore = useUserStore();
  const telCodeExpireDate = ref(dayjs(new Date()));
  const telCodeDuration = ref(0);
  const loginForm = reactive({
    username: '',
    credentials: '',
    loginType: 2,
    clientId: 'APP',
    miniprogramCode: null,
  });
  const agreeCheck = ref([false]);

  const bindCredentialsCode = debounce(function (e) {
    loginForm.credentials = e;
  }, 500);

  const allowLogin = computed(() => {
    return loginForm.username != '' && loginForm.credentials != '' && agreeCheck.value[0];
  });
  function bindCodeChange(val: string) {
    loginForm.credentials = val?.length > 6 ? val.substring(0, 6) : val;
  }
  function getCode(e) {
    if (!loginForm.username) {
      if (loginForm.loginType == 2) {
        AppToast.error('请输入手机号');
      } else {
        AppToast.error('请输入账号');
      }
      return;
    }
    AppToast.loading();
    AuthApi.getTelCode(loginForm.username)
      .then(() => {
        AppToast.success('验证码已发送');
        telCodeDuration.value = 60;
        telCodeExpireDate.value = dayjs().add(60, 'second');
        const timer = setInterval(() => {
          if (telCodeDuration.value > 0) {
            telCodeDuration.value = telCodeExpireDate.value.diff(dayjs(new Date()), 's');
          } else {
            telCodeDuration.value = 0;
            clearInterval(timer);
          }
        }, 1000);
      })
      .finally(() => {
        AppToast.close();
      });
  }
  function switchLoginType(value) {
    loginForm.username = '';
    loginForm.credentials = '';
    loginForm.loginType = value;
  }
  function changeAgree() {
    agreeCheck.value = [!agreeCheck.value[0]];
  }
  function toRegister() {
    AppRouter.navTo('./register-terms');
  }
  function toPrivacy() {
    AppRouter.navTo('/pages/public/privacy')
  }
  function toAgreement() {
    AppRouter.navTo('/pages/public/agreement')
  }
  function toService() {
    uni.makePhoneCall({
      phoneNumber: '4006639993', //电话号码
      success: function (e) {},
      fail: function (e) {},
    })
  }
  function toResetPassword() {
    AppRouter.navTo('/pages/public/reset-password');
  }
  async function login() {
    const loginData = { ...loginForm };
    if (!loginData.username) {
      if (loginData.loginType == 2) {
        AppToast.error('请输入手机号');
      } else {
        AppToast.error('请输入账号');
      }
      return;
    }
    if (!loginData.credentials) {
      if (loginData.loginType == 2) {
        AppToast.error('请输入验证码');
      } else {
        AppToast.error('请输入密码');
      }
      return;
    }
    if (!agreeCheck.value[0]) {
      AppToast.error('请阅读并同意《服务协议》和《隐私政策》');
      return;
    }
    // #ifdef MP-WEIXIN
    try {
      const { code: miniprogramCode } = await uni.login({ onlyAuthorize: true });
      loginData.miniprogramCode = miniprogramCode;
    } catch (e) {
      /* empty */
    }
    // #endif
    AppToast.loading();
    AuthApi.login(loginData)
      .then((r) => {
        AppGlobal.CurrentUser.setToken(r.accessToken);
        userStore.loadUserInfo().then((r) => {
          if (r.auditStatus == 1) {
            AppRouter.reLaunchTo('/pages/index/index');
          } else {
            AppRouter.reLaunchTo('/pages/index/audit');
          }
        });
      })
      .finally(() => {
        AppToast.close();
      });
  }
  onShow(() => {
    AppGlobal.CurrentUser.logout();
    // #ifdef MP-WEIXIN
    uni.hideHomeButton();
    loginForm.clientId = 'MP_MINIAPP';
    // #endif
  });
</script>

<style lang="scss" scoped>
  .login-page {
    padding: 0 32rpx;
    padding-top: 64rpx;
    // #ifdef MP-WEIXIN
    padding-top: 120rpx;
    // #endif
    box-sizing: border-box;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .content-bottom {
    margin-top: auto;
    margin-bottom: $uni-spacing-row-base;
  }

  .logo {
    width: 200rpx;
    height: 200rpx;
    margin-top: 64rpx;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 80rpx;
  }

  .login-form {
    width: 100%;
    margin-top: $uni-spacing-col-lg;
    display: flex;
    flex-direction: column;
    justify-content: center;
    &__title {
      font-weight: bold;
    }
    &__item {
      margin-bottom: 64rpx;
      :deep(.uni-easyinput__content) {
        border: 2rpx solid #DAE2E5!important;
      }
      :deep(.uni-easyinput__placeholder-class) {
        color: #ADBCBF!important;
      }
    }
    &__getCode {
      opacity: 0.6;
      margin-right: $uni-spacing-row-base;
    }
  }
  .tooltip {
    display: flex;
    justify-content: space-between;
    text {
      color: #727D7F;
    }
  }
  .login-btn {
    width: 60%;
    margin-top: 80rpx;
    // border-radius: 64px;
  }
  .agree-label {
    margin-top: $uni-spacing-col-lg;
    font-size: 28rpx;
    display: flex;
    justify-content: center;

    .agree-checkbox {
      transform: scale(0.7);
    }
  }
  .bottom-toolbar {
    display: flex;
    justify-content: space-between;
    margin: 0 160rpx 40rpx 160rpx;
    color: $uni-color-primary;
  }
  .bottom-tip {
    text-align: center;
    color: $uni-text-color-grey;
    font-size: 26rpx;
    margin-bottom: $uni-spacing-col-base;
  }
  .login-tip {
    font-size: 32rpx;
    color: $uni-text-color-grey;
    margin-bottom: 40rpx;
  }
  .login-type-tip {
    font-size: 40rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
  }
  .service-tip {
    color: $uni-color-primary;
  }
</style>
